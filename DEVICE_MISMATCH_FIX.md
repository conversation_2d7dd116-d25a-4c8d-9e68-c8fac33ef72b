# Device Mismatch Fix for Multi-GPU SAE Training

## Problem Description

The error you encountered was:
```
ERROR:itas.core.activations_store:Forward pass failed: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cuda:1!
```

This occurred because when using multi-GPU setups with `device_map="auto"`, the model layers are distributed across multiple GPUs, but the input tensors were being moved to `self.model.device` which might not be the correct device for all layers.

## Root Cause

1. **Model Loading**: The model was loaded with `device_map="auto"` which distributes layers across GPUs
2. **Device Conflict**: The code was moving input tensors to `self.model.device` (typically cuda:0)
3. **Layer Distribution**: Some model layers (especially later layers) were on cuda:1
4. **Mismatch**: Input tensors on cuda:0 couldn't be processed by layers on cuda:1

## Solution Implemented

### 1. Enhanced Device Detection in ActivationsStore

**File**: `itas/core/activations_store.py`

Added a new method `_get_input_device()` that intelligently detects the correct device for input tensors:

```python
def _get_input_device(self) -> torch.device:
    """
    Get the device where input tensors should be placed.
    
    For multi-GPU models with device_map, this finds the device of the
    embedding layer, which is where inputs need to be placed.
    """
    # Try to find the embedding layer device
    embedding_layer_names = [
        "embed_tokens",      # LLaMA, Mistral
        "wte",              # GPT-2, GPT-J
        "word_embeddings",  # BERT, RoBERTa
        "embeddings",       # General
        "transformer.wte",  # GPT-2 style
        "model.embed_tokens", # LLaMA style
    ]
    
    for layer_name in embedding_layer_names:
        # Navigate to the embedding layer and get its device
        # ... (implementation details)
    
    # Fallback to first parameter device
    return next(self.model.parameters()).device
```

### 2. Updated Device Placement Logic

**Changed from**:
```python
input_ids = batch["input_ids"].to(self.model.device)
```

**Changed to**:
```python
input_ids = batch["input_ids"].to(self._input_device)
```

This ensures input tensors are placed on the same device as the embedding layer.

### 3. Fixed Model Setup in SAETrainer

**File**: `sae_train.py`

**Before**:
```python
# For multi-GPU, ensure model is on primary device before DataParallel wrapping
if self.num_gpus > 1:
    self.model = self.model.to(self.device)
```

**After**:
```python
# For multi-GPU with device_map="auto", don't move the model to a single device
# The device_map="auto" already handles optimal device placement
if self.num_gpus > 1:
    print(f"🔧 Model loaded with device_map='auto' across {self.num_gpus} GPUs")
    print(f"   Model will use automatic device placement for optimal memory usage")
else:
    # For single GPU, ensure model is on the correct device
    self.model = self.model.to(self.device)
```

### 4. Improved Device Map Configuration

**File**: `sae_train.py`

```python
def create_config(num_gpus=1, args=None):
    # For multi-GPU, use auto device mapping for optimal memory distribution
    # For single GPU, use explicit device to avoid unnecessary complexity
    if num_gpus > 1:
        device_map = "auto"
        print(f"🔧 Using device_map='auto' for {num_gpus} GPUs")
    else:
        device_map = None  # Let the model loader handle single GPU placement
        print(f"🔧 Using single GPU device placement")
```

## Key Changes Summary

1. **Smart Device Detection**: Automatically finds the correct input device by locating the embedding layer
2. **Preserved Model Distribution**: Doesn't interfere with `device_map="auto"` placement
3. **Consistent Tensor Placement**: All input tensors go to the embedding layer device
4. **Better Logging**: Added debug information to track device placement

## Testing

A test script `test_device_fix.py` was created to verify the fix works correctly. Run it with:

```bash
python test_device_fix.py
```

## Expected Behavior After Fix

1. **Multi-GPU**: Model layers distributed optimally across GPUs with `device_map="auto"`
2. **Input Placement**: Input tensors automatically placed on the correct device
3. **No Device Errors**: Forward passes should complete without device mismatch errors
4. **Optimal Memory Usage**: Model uses available GPU memory efficiently

## Verification

After applying these changes, your SAE training should proceed without the device mismatch error. The logs will show:

```
🎯 Input device for activations store: cuda:0
🔧 Model loaded with device_map='auto' across 2 GPUs
   Model will use automatic device placement for optimal memory usage
```

This indicates the fix is working correctly and the model is properly distributed across your available GPUs.
